/**
 * CozyWish Navbar Design System
 * Professional navbar styling based on design reference
 */

/* CSS Custom Properties for Navbar */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-50: #fafafa;
    --cw-neutral-100: #f5f5f5;
    --cw-neutral-200: #e5e5e5;
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Navbar Base Styles */
.navbar-cw {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--cw-shadow-sm);
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    font-family: var(--cw-font-primary);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.navbar-cw.navbar-hero {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
}

/* Brand Styling */
.navbar-brand-cw {
    font-family: var(--cw-font-heading);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--cw-brand-primary) !important;
    text-decoration: none;
    letter-spacing: -0.02em;
    transition: all 0.2s ease;
}

.navbar-brand-cw:hover {
    color: var(--cw-brand-light) !important;
    transform: translateY(-1px);
}

/* Navigation Button Styles */
.btn-cw-nav-primary {
    background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    font-family: var(--cw-font-primary);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-cw-nav-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    color: white;
    text-decoration: none;
}

.btn-cw-nav-secondary {
    border: 2px solid var(--cw-brand-primary);
    color: var(--cw-brand-primary);
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    background: white;
    transition: all 0.2s ease;
    font-family: var(--cw-font-primary);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-cw-nav-secondary:hover {
    background: var(--cw-brand-primary);
    color: white;
    transform: translateY(-1px);
    text-decoration: none;
}

.btn-cw-nav-secondary.active {
    background: var(--cw-brand-primary);
    color: white;
}

.btn-cw-nav-icon {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--cw-neutral-50);
    border: 1px solid var(--cw-neutral-200);
    color: var(--cw-neutral-700);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 1rem;
}

.btn-cw-nav-icon:hover {
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    transform: translateY(-1px);
    text-decoration: none;
    box-shadow: var(--cw-shadow-sm);
}

.btn-cw-nav-ghost {
    color: var(--cw-brand-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    transition: all 0.2s ease;
    font-family: var(--cw-font-primary);
    border-radius: 0.375rem;
}

.btn-cw-nav-ghost:hover {
    color: var(--cw-brand-light);
    background: var(--cw-accent-light);
    text-decoration: none;
}

/* Notification Badge */
.badge-cw-notification {
    background: #dc2626;
    color: white;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    min-width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dropdown Menu Styling */
.dropdown-menu-cw {
    border: 1px solid var(--cw-neutral-200);
    border-radius: 0.75rem;
    box-shadow: var(--cw-shadow-md);
    background: white;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 280px;
}

.dropdown-item-cw {
    font-family: var(--cw-font-primary);
    font-weight: 400;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
    color: var(--cw-neutral-800);
    text-decoration: none;
    display: flex;
    align-items: center;
    border: none;
    background: none;
    width: 100%;
}

.dropdown-item-cw:hover {
    background-color: var(--cw-accent-light);
    color: var(--cw-brand-primary);
    text-decoration: none;
}

.dropdown-item-cw i {
    width: 1.25rem;
    text-align: center;
    margin-right: 0.75rem;
    color: var(--cw-neutral-600);
}

.dropdown-item-cw:hover i {
    color: var(--cw-brand-primary);
}

.dropdown-divider-cw {
    height: 1px;
    background: var(--cw-neutral-200);
    margin: 0.5rem 1rem;
    border: none;
}

.dropdown-header-cw {
    padding: 0.75rem 1.5rem 0.5rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Body padding for fixed navbar */
body {
    padding-top: 70px;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    body {
        padding-top: 65px;
    }

    .navbar-cw {
        padding: 0.4rem 0;
    }

    .navbar-brand-cw {
        font-size: 1.5rem;
    }

    .btn-cw-nav-primary,
    .btn-cw-nav-secondary {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .btn-cw-nav-icon {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }

    .dropdown-menu-cw {
        min-width: 260px;
    }
}

/* Navbar Toggle Button */
.navbar-toggler-cw {
    border: 2px solid var(--cw-brand-primary);
    border-radius: 0.375rem;
    padding: 0.5rem;
    background: transparent;
    transition: all 0.2s ease;
}

.navbar-toggler-cw:hover {
    background: var(--cw-brand-primary);
}

.navbar-toggler-cw:hover .navbar-toggler-icon-cw {
    background-color: white;
}

.navbar-toggler-icon-cw {
    width: 1.5rem;
    height: 1.5rem;
    background-color: var(--cw-brand-primary);
    transition: all 0.2s ease;
}

/* Navigation Container */
.nav-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* User Avatar in Dropdown */
.user-avatar-cw {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    border: 2px solid var(--cw-brand-primary);
}

/* Badge for menu items */
.menu-badge-cw {
    background: var(--cw-brand-primary);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 50px;
    margin-left: auto;
    font-weight: 500;
}

/* Enhanced hover effects */
.btn-cw-nav-primary:focus,
.btn-cw-nav-secondary:focus,
.btn-cw-nav-icon:focus {
    outline: 2px solid var(--cw-brand-primary);
    outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.dropdown-menu-cw {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.dropdown-menu-cw.show {
    animation: fadeInDown 0.2s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional spacing for navbar items */
.nav-buttons > * {
    margin-left: 0.5rem;
}

.nav-buttons > *:first-child {
    margin-left: 0;
}

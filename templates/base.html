<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- External CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/base.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar.css' %}" rel="stylesheet">

    <!-- Additional head content -->
    {% block extra_head %}{% endblock %}
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}" data-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Navigation Bar -->
    {% include 'includes/navbar_cw.html' %}

    {% if hero_section %}
        <!-- Hero page content with gradient background -->
        <div class="radial-gradient">
            <!-- Hero section content -->
            {% block hero_content %}{% endblock %}
        </div>
    {% endif %}


    <!-- System Messages -->
    {% if messages %}
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
            {% for message in messages %}
                <div class="toast text-bg-{{ message.tags }} mb-2" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">{{ message }}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content Area -->
    <main class="container py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Site Footer -->
    <footer class="footer-cw bg-white border-top">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold text-secondary-cw mb-3">CozyWish</h5>
                    <p class="text-neutral-cw mb-2">Find & Book Local Spa and Massage Services</p>
                    <p class="text-neutral-cw">Professional wellness marketplace connecting you with local spa and massage providers.</p>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Find Services</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Massage</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Facial</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Spa</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Company</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'home' %}" class="text-neutral-cw text-decoration-none footer-link">Home</a></li>
                        <li class="mb-2"><a href="{% url 'accounts_app:business_landing' %}" class="text-neutral-cw text-decoration-none footer-link">For Business</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">About</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Help Center</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Privacy Policy</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Terms of Service</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Connect</h6>
                    <div class="d-flex gap-2 mb-3">
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                    <div class="contact-info">
                        <p class="text-neutral-cw mb-1 small"><i class="fas fa-phone me-2"></i> (*************</p>
                        <p class="text-neutral-cw mb-0 small"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-neutral-cw mb-0">&copy; {% now "Y" %} CozyWish. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-neutral-cw mb-0">Professional Wellness Marketplace</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>

    <!-- Custom JavaScript Files -->
    <script src="{% static 'js/messages.js' %}"></script>
    <script src="{% static 'js/discounts.js' %}"></script>
    <script src="{% static 'js/payments.js' %}"></script>
    <script src="{% static 'js/form_loading.js' %}"></script>
    <script src="{% static 'js/notifications.js' %}"></script>

    <!-- Bootstrap Tooltip Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        });
    </script>

    <!-- Additional JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>